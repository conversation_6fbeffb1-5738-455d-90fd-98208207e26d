// @ts-check

/**
 * Scene Detection Tool
 *
 * Intelligently detects scene boundaries in subtitle content by analyzing:
 * - Speaker changes and dialogue patterns
 * - Timing gaps between lines
 * - Content transitions and topic shifts
 * - Natural conversation breaks
 *
 * This enables dynamic chunking that respects narrative flow rather than
 * using arbitrary line counts.
 */

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  GRAY: '\x1b[90m',
  SUCCESS: '\x1b[32m\x1b[1m',
  INFO: '\x1b[36m',
  DEBUG: '\x1b[90m',
  SCENE: '\x1b[34m\x1b[1m'
};

export class SceneDetector {
  constructor(options = {}) {
    this.minSceneLength = options.minSceneLength || 4;
    this.maxSceneLength = options.maxSceneLength || 60;
    this.timingGapThreshold = options.timingGapThreshold || 5000; // 5 seconds in ms
    this.speakerChangeWeight = options.speakerChangeWeight || 0.3;
    this.timingWeight = options.timingWeight || 0.4;
    this.contentWeight = options.contentWeight || 0.3;
    this.enforceMaxLength = options.enforceMaxLength !== false; // Default to true
    this.enforceMinLength = options.enforceMinLength !== false; // Default to true
    this.fallbackSplitThreshold = options.fallbackSplitThreshold || 60;

    console.log(`${COLORS.SUCCESS}🎬 [SceneDetector] Initialized with intelligent scene detection (min: ${this.minSceneLength}, max: ${this.maxSceneLength} lines)${COLORS.RESET}`);
  }

  /**
   * Detect scenes in subtitle content
   * @param {string} subtitleContent - Raw subtitle content
   * @returns {Promise<Array>} - Array of scene objects
   */
  async detectScenes(subtitleContent) {
    const lines = this.parseSubtitleLines(subtitleContent);
    if (lines.length === 0) {
      return [];
    }

    const sceneBreaks = await this.findSceneBreaks(lines);
    const scenes = this.createScenes(lines, sceneBreaks);

    console.log(`${COLORS.SCENE}📋 [SceneDetector] Detected ${scenes.length} scenes from ${lines.length} lines${COLORS.RESET}`);
    scenes.forEach((scene, i) => {
      console.log(`${COLORS.DEBUG}  🎬 Scene ${i + 1}: ${scene.lines.length} lines, speakers: ${scene.speakers.join(', ')}, tone: ${scene.emotionalTone}${COLORS.RESET}`);
    });
    return scenes;
  }

  /**
   * Parse subtitle content into structured line objects
   * @param {string} content - Raw subtitle content
   * @returns {Array} - Array of parsed line objects
   */
  parseSubtitleLines(content) {
    const lines = content.split('\n').filter(line => line.trim());
    const parsedLines = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Extract timestamp, speaker and dialogue from new format: "0:00:05.23-0:00:07.45 | Actor | Dialogue"
      const parts = line.split(' | ');
      if (parts.length < 3) {
        // Fallback for old format without timestamps: "Actor | Dialogue"
        const colonIndex = line.indexOf('|');
        if (colonIndex === -1) continue;

        const speaker = line.slice(0, colonIndex).trim().toLowerCase();
        const dialogue = line.slice(colonIndex + 1).trim();

        if (!dialogue) continue;

        parsedLines.push({
          index: i,
          original: line,
          speaker: speaker,
          dialogue: dialogue,
          timestamp: null,
          wordCount: dialogue.split(/\s+/).length,
          hasAction: this.detectActionLine(dialogue),
          hasEmphasis: this.detectEmphasis(dialogue),
          isQuestion: dialogue.includes('?'),
          isExclamation: dialogue.includes('!')
        });
        continue;
      }

      // New format with timestamps
      const timestampPart = parts[0].trim();
      const speaker = parts[1].trim().toLowerCase();
      const dialogue = parts[2].trim();

      if (!dialogue) continue;

      parsedLines.push({
        index: i,
        original: line,
        speaker: speaker,
        dialogue: dialogue,
        timestamp: this.extractTimestamp(line), // Parse timestamp from the line
        wordCount: dialogue.split(/\s+/).length,
        hasAction: this.detectActionLine(dialogue),
        hasEmphasis: this.detectEmphasis(dialogue),
        isQuestion: dialogue.includes('?'),
        isExclamation: dialogue.includes('!')
      });
    }

    return parsedLines;
  }

  /**
   * Find potential scene break points
   * @param {Array} lines - Parsed line objects
   * @returns {Promise<Array>} - Array of scene break indices
   */
  async findSceneBreaks(lines) {
    const breakScores = [];
    
    for (let i = 1; i < lines.length; i++) {
      const score = this.calculateBreakScore(lines, i);
      breakScores.push({ index: i, score });
    }

    // Sort by score and select top candidates
    breakScores.sort((a, b) => b.score - a.score);
    
    // Select breaks that create reasonable scene lengths
    const selectedBreaks = this.selectOptimalBreaks(breakScores, lines.length);
    
    return selectedBreaks.sort((a, b) => a - b);
  }

  /**
   * Calculate scene break score for a given position
   * @param {Array} lines - All lines
   * @param {number} index - Position to evaluate
   * @returns {number} - Break score (higher = more likely break)
   */
  calculateBreakScore(lines, index) {
    const currentLine = lines[index];
    const previousLine = lines[index - 1];
    
    let score = 0;

    // Speaker change score
    if (currentLine.speaker !== previousLine.speaker) {
      score += this.speakerChangeWeight;
      
      // Bonus for returning to a previous speaker after dialogue
      const speakerHistory = lines.slice(Math.max(0, index - 5), index)
        .map(l => l.speaker);
      if (speakerHistory.includes(currentLine.speaker)) {
        score += 0.1;
      }
    }

    // Content transition score
    score += this.calculateContentTransitionScore(lines, index) * this.contentWeight;

    // Timing gap score (if timestamps available)
    if (currentLine.timestamp && previousLine.timestamp) {
      const timingGap = currentLine.timestamp - previousLine.timestamp;
      if (timingGap > this.timingGapThreshold) {
        score += this.timingWeight * Math.min(1, timingGap / (this.timingGapThreshold * 3));
      }
    }

    // Action line boundaries
    if (currentLine.hasAction || previousLine.hasAction) {
      score += 0.2;
    }

    // Question/answer patterns
    if (previousLine.isQuestion && !currentLine.isQuestion) {
      score += 0.15;
    }

    // Emotional shift detection
    if (this.detectEmotionalShift(previousLine, currentLine)) {
      score += 0.1;
    }

    return score;
  }

  /**
   * Calculate content transition score based on dialogue similarity
   * @param {Array} lines - All lines
   * @param {number} index - Current position
   * @returns {number} - Content transition score
   */
  calculateContentTransitionScore(lines, index) {
    const windowSize = 3;
    const beforeWindow = lines.slice(Math.max(0, index - windowSize), index);
    const afterWindow = lines.slice(index, Math.min(lines.length, index + windowSize));

    if (beforeWindow.length === 0 || afterWindow.length === 0) {
      return 0;
    }

    // Calculate vocabulary overlap
    const beforeWords = new Set(
      beforeWindow.flatMap(l => l.dialogue.toLowerCase().split(/\s+/))
    );
    const afterWords = new Set(
      afterWindow.flatMap(l => l.dialogue.toLowerCase().split(/\s+/))
    );

    const intersection = new Set([...beforeWords].filter(w => afterWords.has(w)));
    const union = new Set([...beforeWords, ...afterWords]);
    
    const similarity = intersection.size / union.size;
    
    // Lower similarity = higher transition score
    return 1 - similarity;
  }

  /**
   * Select optimal break points to create well-sized scenes
   * @param {Array} breakCandidates - Sorted break candidates
   * @param {number} totalLines - Total number of lines
   * @returns {Array} - Selected break indices
   */
  selectOptimalBreaks(breakCandidates, totalLines) {
    const selectedBreaks = [];
    let lastBreak = 0;

    for (const candidate of breakCandidates) {
      const sceneLength = candidate.index - lastBreak;
      
      // Only select if it creates a reasonable scene length
      if (sceneLength >= this.minSceneLength && 
          candidate.score > 0.3 && // Minimum quality threshold
          (selectedBreaks.length === 0 || candidate.index - selectedBreaks[selectedBreaks.length - 1] >= this.minSceneLength)) {
        
        selectedBreaks.push(candidate.index);
        lastBreak = candidate.index;
        
        // Don't create too many small scenes
        if (totalLines - lastBreak < this.minSceneLength * 2) {
          break;
        }
      }
    }

    return selectedBreaks;
  }

  /**
   * Create scene objects from lines and break points
   * @param {Array} lines - All parsed lines
   * @param {Array} breaks - Scene break indices
   * @returns {Array} - Scene objects
   */
  createScenes(lines, breaks) {
    const scenes = [];
    let startIndex = 0;

    // Add break at the end
    const allBreaks = [...breaks, lines.length];

    for (const breakIndex of allBreaks) {
      const sceneLines = lines.slice(startIndex, breakIndex);
      if (sceneLines.length === 0) continue;

      // Enforce maximum scene length if enabled
      if (this.enforceMaxLength && sceneLines.length > this.fallbackSplitThreshold) {
        console.log(`${COLORS.WARNING}⚠️  [SceneDetector] Scene too large (${sceneLines.length} lines), splitting...${COLORS.RESET}`);
        const splitScenes = this.splitOversizedScene(sceneLines, startIndex);
        scenes.push(...splitScenes);
      } else {
        const scene = {
          index: scenes.length,
          startLine: startIndex,
          endLine: breakIndex - 1,
          lines: sceneLines,
          content: sceneLines.map(l => l.original).join('\n'),
          speakers: [...new Set(sceneLines.map(l => l.speaker))],
          timestamp: sceneLines[0].timestamp,
          duration: this.calculateSceneDuration(sceneLines),
          wordCount: sceneLines.reduce((sum, l) => sum + l.wordCount, 0),
          hasAction: sceneLines.some(l => l.hasAction),
          emotionalTone: this.analyzeEmotionalTone(sceneLines)
        };

        scenes.push(scene);
      }

      startIndex = breakIndex;
    }

    // Post-process scenes to enforce minimum length
    if (this.enforceMinLength) {
      return this.enforceMinimumSceneLength(scenes, lines);
    }

    return scenes;
  }

  /**
   * Split an oversized scene into smaller scenes
   * @param {Array} sceneLines - Lines in the oversized scene
   * @param {number} baseStartIndex - Base start index for line numbering
   * @returns {Array} - Array of smaller scene objects
   */
  splitOversizedScene(sceneLines, baseStartIndex) {
    const splitScenes = [];
    let currentStart = 0;

    while (currentStart < sceneLines.length) {
      const remainingLines = sceneLines.length - currentStart;
      let chunkSize = Math.min(this.maxSceneLength, remainingLines);

      // Try to find a good break point within the chunk
      if (chunkSize < remainingLines) {
        const searchEnd = Math.min(currentStart + chunkSize + 10, sceneLines.length);
        const searchStart = currentStart + Math.max(this.minSceneLength, chunkSize - 10);

        // Look for speaker changes or natural breaks
        for (let i = searchEnd - 1; i >= searchStart; i--) {
          if (i > currentStart &&
              (sceneLines[i].speaker !== sceneLines[i-1].speaker ||
               sceneLines[i-1].isQuestion ||
               sceneLines[i-1].dialogue.endsWith('.'))) {
            chunkSize = i - currentStart;
            break;
          }
        }
      }

      const chunkLines = sceneLines.slice(currentStart, currentStart + chunkSize);
      const scene = {
        index: splitScenes.length,
        startLine: baseStartIndex + currentStart,
        endLine: baseStartIndex + currentStart + chunkSize - 1,
        lines: chunkLines,
        content: chunkLines.map(l => l.original).join('\n'),
        speakers: [...new Set(chunkLines.map(l => l.speaker))],
        timestamp: chunkLines[0].timestamp,
        duration: this.calculateSceneDuration(chunkLines),
        wordCount: chunkLines.reduce((sum, l) => sum + l.wordCount, 0),
        hasAction: chunkLines.some(l => l.hasAction),
        emotionalTone: this.analyzeEmotionalTone(chunkLines)
      };

      splitScenes.push(scene);
      currentStart += chunkSize;
    }

    console.log(`${COLORS.INFO}📋 [SceneDetector] Split into ${splitScenes.length} smaller scenes${COLORS.RESET}`);
    return splitScenes;
  }

  /**
   * Enforce minimum scene length by merging small scenes with adjacent ones
   * @param {Array} scenes - Array of scene objects
   * @param {Array} allLines - All parsed lines for reference
   * @returns {Array} - Array of scenes with enforced minimum length
   */
  enforceMinimumSceneLength(scenes, allLines) {
    if (scenes.length <= 1) return scenes;

    const mergedScenes = [];
    let i = 0;

    while (i < scenes.length) {
      let currentScene = scenes[i];

      // If scene is too small, try to merge with next scene
      if (currentScene.lines.length < this.minSceneLength && i < scenes.length - 1) {
        const nextScene = scenes[i + 1];

        // Check if merging would create a scene that's too large
        const combinedLength = currentScene.lines.length + nextScene.lines.length;

        if (combinedLength <= this.maxSceneLength) {
          console.log(`${COLORS.WARNING}⚠️  [SceneDetector] Scene ${i + 1} too small (${currentScene.lines.length} lines), merging with next scene...${COLORS.RESET}`);

          // Merge the scenes
          const mergedLines = [...currentScene.lines, ...nextScene.lines];
          const mergedScene = {
            index: mergedScenes.length,
            startLine: currentScene.startLine,
            endLine: nextScene.endLine,
            lines: mergedLines,
            content: mergedLines.map(l => l.original).join('\n'),
            speakers: [...new Set(mergedLines.map(l => l.speaker))],
            timestamp: currentScene.timestamp,
            duration: this.calculateSceneDuration(mergedLines),
            wordCount: mergedLines.reduce((sum, l) => sum + l.wordCount, 0),
            hasAction: mergedLines.some(l => l.hasAction),
            emotionalTone: this.analyzeEmotionalTone(mergedLines)
          };

          mergedScenes.push(mergedScene);
          i += 2; // Skip both scenes as they've been merged
        } else {
          // Can't merge without exceeding max length, keep as is
          console.log(`${COLORS.WARNING}⚠️  [SceneDetector] Scene ${i + 1} too small (${currentScene.lines.length} lines) but can't merge (would exceed max length)${COLORS.RESET}`);
          currentScene.index = mergedScenes.length;
          mergedScenes.push(currentScene);
          i++;
        }
      } else {
        // Scene meets minimum length requirement or is the last scene
        currentScene.index = mergedScenes.length;
        mergedScenes.push(currentScene);
        i++;
      }
    }

    console.log(`${COLORS.INFO}📋 [SceneDetector] After minimum length enforcement: ${mergedScenes.length} scenes${COLORS.RESET}`);
    return mergedScenes;
  }

  /**
   * Extract timestamp from line (for ASS format integration)
   * @param {string} line - Subtitle line
   * @returns {number|null} - Timestamp in milliseconds or null
   */
  extractTimestamp(line) {
    // Parse the new timestamp format: "0:00:05.23-0:00:07.45 | Actor | Dialogue"
    const timestampMatch = line.match(/^(\d+:\d+:\d+\.\d+)-(\d+:\d+:\d+\.\d+)\s*\|/);
    if (timestampMatch) {
      const rawTimestamp = timestampMatch[1];
      const startTime = this.parseTimeToMilliseconds(rawTimestamp);
      console.log(`${COLORS.DEBUG}🕐 [SceneDetector] Raw timestamp: "${rawTimestamp}" -> ${startTime}ms${COLORS.RESET}`);
      return startTime;
    }

    // Fallback for old format without timestamps
    return null;
  }

  /**
   * Convert time string to milliseconds
   * @param {string} timeStr - Time in format "0:00:05.23"
   * @returns {number} - Time in milliseconds
   */
  parseTimeToMilliseconds(timeStr) {
    const parts = timeStr.split(':');
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    const secondsParts = parts[2].split('.');
    const seconds = parseInt(secondsParts[0], 10);
    const centiseconds = parseInt(secondsParts[1], 10);

    return (hours * 3600 + minutes * 60 + seconds) * 1000 + centiseconds * 10;
  }

  /**
   * Detect if line contains action descriptions
   * @param {string} dialogue - Dialogue text
   * @returns {boolean} - True if contains action
   */
  detectActionLine(dialogue) {
    // TODO: detect in a more robust way
    return false;
  }

  /**
   * Detect emphasis in dialogue
   * @param {string} dialogue - Dialogue text
   * @returns {boolean} - True if has emphasis
   */
  detectEmphasis(dialogue) {
    return dialogue.includes('!') || 
           dialogue.includes('...') || 
           dialogue.toUpperCase() === dialogue ||
           dialogue.includes('—');
  }

  /**
   * Detect emotional shift between lines
   * @param {Object} prevLine - Previous line
   * @param {Object} currLine - Current line
   * @returns {boolean} - True if emotional shift detected
   */
  detectEmotionalShift(prevLine, currLine) {
    // Simple heuristic: change from calm to excited or vice versa
    const prevExcited = prevLine.isExclamation || prevLine.hasEmphasis;
    const currExcited = currLine.isExclamation || currLine.hasEmphasis;
    
    return prevExcited !== currExcited;
  }

  /**
   * Calculate scene duration if timestamps available
   * @param {Array} sceneLines - Lines in the scene
   * @returns {number|null} - Duration in milliseconds or null
   */
  calculateSceneDuration(sceneLines) {
    const timestamps = sceneLines
      .map(l => l.timestamp)
      .filter(t => t !== null);
    
    if (timestamps.length < 2) return null;
    
    return Math.max(...timestamps) - Math.min(...timestamps);
  }

  /**
   * Analyze emotional tone of scene
   * @param {Array} sceneLines - Lines in the scene
   * @returns {string} - Emotional tone description
   */
  analyzeEmotionalTone(sceneLines) {
    const questionCount = sceneLines.filter(l => l.isQuestion).length;
    const exclamationCount = sceneLines.filter(l => l.isExclamation).length;
    const actionCount = sceneLines.filter(l => l.hasAction).length;
    const emphasisCount = sceneLines.filter(l => l.hasEmphasis).length;
    
    const totalLines = sceneLines.length;
    
    if (exclamationCount / totalLines > 0.4) return 'excited';
    if (questionCount / totalLines > 0.3) return 'inquisitive';
    if (actionCount / totalLines > 0.2) return 'action-heavy';
    if (emphasisCount / totalLines > 0.3) return 'dramatic';
    
    return 'conversational';
  }
}
