/**
 * Screenshot Tool
 * 
 * Captures frames from video files at specific timestamps to provide
 * visual context for translation decisions. This helps with:
 * - Understanding visual gags and references
 * - Identifying character emotions and expressions
 * - Contextualizing dialogue with on-screen action
 * - Handling text overlays and signs
 */

import * as cp from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(cp.exec);

export class ScreenshotTool {
  constructor(options = {}) {
    this.outputDir = options.outputDir || '2translate/screenshots';
    this.quality = options.quality || 2; // FFmpeg quality scale (1-31, lower is better)
    this.format = options.format || 'png';
    this.maxWidth = options.maxWidth || 1280;
    this.tempDir = path.join(this.outputDir, 'temp');
    
    this.ensureDirectories();
    console.log('[ScreenshotTool] Initialized with output directory:', this.outputDir);
  }

  /**
   * Capture a frame from video at specified timestamp
   * @param {string} videoPath - Path to video file
   * @param {string|number} timestamp - Timestamp (HH:MM:SS or seconds)
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Screenshot metadata and path
   */
  async captureFrame(videoPath, timestamp, options = {}) {
    try {
      if (!fs.existsSync(videoPath)) {
        throw new Error(`Video file not found: ${videoPath}`);
      }

      const normalizedTimestamp = this.normalizeTimestamp(timestamp);
      const outputPath = this.generateOutputPath(videoPath, normalizedTimestamp);
      
      console.log(`[ScreenshotTool] Capturing frame at ${normalizedTimestamp} from ${path.basename(videoPath)}`);
      
      await this.extractFrame(videoPath, normalizedTimestamp, outputPath, options);
      
      const metadata = await this.analyzeScreenshot(outputPath);
      
      return {
        path: outputPath,
        timestamp: normalizedTimestamp,
        videoPath: videoPath,
        metadata: metadata,
        size: this.getFileSize(outputPath)
      };
      
    } catch (error) {
      console.error('[ScreenshotTool] Frame capture failed:', error.message);
      throw error;
    }
  }

  /**
   * Capture multiple frames around a timestamp for context
   * @param {string} videoPath - Path to video file
   * @param {string|number} centerTimestamp - Center timestamp
   * @param {Object} options - Options including frame count and interval
   * @returns {Promise<Array>} - Array of screenshot objects
   */
  async captureFrameSequence(videoPath, centerTimestamp, options = {}) {
    const frameCount = options.frameCount || 3;
    const interval = options.interval || 2; // seconds between frames
    const centerTime = this.timestampToSeconds(centerTimestamp);
    
    const screenshots = [];
    const startTime = centerTime - Math.floor(frameCount / 2) * interval;
    
    for (let i = 0; i < frameCount; i++) {
      const timestamp = startTime + (i * interval);
      if (timestamp >= 0) {
        try {
          const screenshot = await this.captureFrame(videoPath, timestamp, options);
          screenshots.push(screenshot);
        } catch (error) {
          console.warn(`[ScreenshotTool] Failed to capture frame at ${timestamp}s: ${error.message}`);
        }
      }
    }
    
    return screenshots;
  }

  /**
   * Extract frame using FFmpeg
   * @param {string} videoPath - Input video path
   * @param {string} timestamp - Normalized timestamp
   * @param {string} outputPath - Output image path
   * @param {Object} options - Additional FFmpeg options
   * @returns {Promise<void>}
   */
  async extractFrame(videoPath, timestamp, outputPath, options = {}) {
    const ffmpegArgs = [
      '-ss', timestamp,
      '-i', `"${videoPath}"`,
      '-vframes', '1',
      '-q:v', this.quality.toString(),
      '-vf', `scale=${this.maxWidth}:-1`,
      '-y',
      `"${outputPath}"`
    ];

    // Add any custom filters
    if (options.filters) {
      const filterIndex = ffmpegArgs.indexOf('-vf');
      ffmpegArgs[filterIndex + 1] += `,${options.filters}`;
    }

    const command = `ffmpeg ${ffmpegArgs.join(' ')}`;
    
    try {
      const { stdout, stderr } = await execAsync(command, { 
        timeout: 30000,
        windowsHide: true 
      });
      
      if (!fs.existsSync(outputPath)) {
        throw new Error('Screenshot file was not created');
      }
      
      console.log(`[ScreenshotTool] Frame extracted successfully: ${path.basename(outputPath)}`);
      
    } catch (error) {
      throw new Error(`FFmpeg extraction failed: ${error.message}`);
    }
  }

  /**
   * Analyze screenshot for visual elements
   * @param {string} imagePath - Path to screenshot
   * @returns {Promise<Object>} - Analysis results
   */
  async analyzeScreenshot(imagePath) {
    try {
      // Basic image analysis using FFprobe
      const probeCommand = `ffprobe -v quiet -print_format json -show_streams "${imagePath}"`;
      const { stdout } = await execAsync(probeCommand);
      const probeData = JSON.parse(stdout);
      
      const videoStream = probeData.streams.find(s => s.codec_type === 'video');
      
      const metadata = {
        width: videoStream?.width || 0,
        height: videoStream?.height || 0,
        format: videoStream?.codec_name || 'unknown',
        aspectRatio: videoStream ? (videoStream.width / videoStream.height).toFixed(2) : '0',
        colorSpace: videoStream?.color_space || 'unknown',
        hasSubtitles: false, // MKV files don't have baked-in subtitles
        brightness: await this.analyzeBrightness(imagePath),
        timestamp: this.extractTimestampFromPath(imagePath)
      };
      
      return metadata;
      
    } catch (error) {
      console.warn('[ScreenshotTool] Screenshot analysis failed:', error.message);
      return {
        width: 0,
        height: 0,
        format: 'unknown',
        aspectRatio: '0',
        hasSubtitles: false,
        brightness: 'unknown'
      };
    }
  }



  /**
   * Analyze image brightness
   * @param {string} imagePath - Path to image
   * @returns {Promise<string>} - Brightness description
   */
  async analyzeBrightness(imagePath) {
    try {
      // Use FFmpeg to analyze average brightness
      const command = `ffprobe -f lavfi -i "movie=${imagePath},signalstats" -show_entries frame_tags=lavfi.signalstats.YAVG -v quiet -of csv=p=0`;
      const { stdout } = await execAsync(command, { timeout: 10000 });
      
      const brightness = parseFloat(stdout.trim());
      if (brightness < 50) return 'dark';
      if (brightness > 200) return 'bright';
      return 'normal';
      
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Normalize timestamp to FFmpeg format
   * @param {string|number} timestamp - Input timestamp
   * @returns {string} - Normalized timestamp (HH:MM:SS)
   */
  normalizeTimestamp(timestamp) {
    if (typeof timestamp === 'number') {
      return this.secondsToTimestamp(timestamp);
    }
    
    if (typeof timestamp === 'string') {
      // If already in HH:MM:SS format, return as-is
      if (/^\d{1,2}:\d{2}:\d{2}(\.\d+)?$/.test(timestamp)) {
        return timestamp;
      }
      
      // If in seconds, convert
      const seconds = parseFloat(timestamp);
      if (!isNaN(seconds)) {
        return this.secondsToTimestamp(seconds);
      }
    }
    
    throw new Error(`Invalid timestamp format: ${timestamp}`);
  }

  /**
   * Convert seconds to HH:MM:SS format
   * @param {number} seconds - Seconds
   * @returns {string} - Formatted timestamp
   */
  secondsToTimestamp(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 100);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
  }

  /**
   * Convert timestamp to seconds
   * @param {string} timestamp - HH:MM:SS timestamp
   * @returns {number} - Seconds
   */
  timestampToSeconds(timestamp) {
    if (typeof timestamp === 'number') return timestamp;
    
    const parts = timestamp.split(':');
    if (parts.length === 3) {
      const [hours, minutes, seconds] = parts;
      return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds);
    }
    
    return parseFloat(timestamp) || 0;
  }

  /**
   * Generate output path for screenshot
   * @param {string} videoPath - Video file path
   * @param {string} timestamp - Timestamp
   * @returns {string} - Output path
   */
  generateOutputPath(videoPath, timestamp) {
    const videoName = path.basename(videoPath, path.extname(videoPath));
    const safeTimestamp = timestamp.replace(/[:.]/g, '-');
    const filename = `${videoName}_${safeTimestamp}.${this.format}`;
    return path.join(this.outputDir, filename);
  }

  /**
   * Extract timestamp from screenshot filename
   * @param {string} imagePath - Image path
   * @returns {string|null} - Extracted timestamp
   */
  extractTimestampFromPath(imagePath) {
    const filename = path.basename(imagePath, path.extname(imagePath));
    const match = filename.match(/_(\d{2}-\d{2}-\d{2}-\d{2})$/);
    return match ? match[1].replace(/-/g, ':') : null;
  }

  /**
   * Get file size in bytes
   * @param {string} filePath - File path
   * @returns {number} - File size
   */
  getFileSize(filePath) {
    try {
      return fs.statSync(filePath).size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Ensure required directories exist
   * @returns {void}
   */
  ensureDirectories() {
    [this.outputDir, this.tempDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`[ScreenshotTool] Created directory: ${dir}`);
      }
    });
  }

  /**
   * Clean up old screenshots
   * @param {number} maxAge - Maximum age in milliseconds
   * @returns {Promise<void>}
   */
  async cleanup(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    try {
      const files = fs.readdirSync(this.outputDir);
      const now = Date.now();
      
      for (const file of files) {
        const filePath = path.join(this.outputDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          console.log(`[ScreenshotTool] Cleaned up old screenshot: ${file}`);
        }
      }
    } catch (error) {
      console.warn('[ScreenshotTool] Cleanup failed:', error.message);
    }
  }
}
