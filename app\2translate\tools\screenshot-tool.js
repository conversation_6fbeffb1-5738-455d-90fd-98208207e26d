/**
 * Screenshot Tool
 * 
 * Captures frames from video files at specific timestamps to provide
 * visual context for translation decisions. This helps with:
 * - Understanding visual gags and references
 * - Identifying character emotions and expressions
 * - Contextualizing dialogue with on-screen action
 * - Handling text overlays and signs
 */

import * as cp from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(cp.exec);

export class ScreenshotTool {
  constructor(options = {}) {
    this.outputDir = options.outputDir || '2translate/screenshots';
    this.quality = options.quality || 2; // FFmpeg quality scale (1-31, lower is better)
    this.format = options.format || 'png';
    this.maxWidth = options.maxWidth || 1280;
    this.tempDir = path.join(this.outputDir, 'temp');
    
    this.ensureDirectories();
    console.log('[ScreenshotTool] Initialized with output directory:', this.outputDir);
  }

  /**
   * Capture a frame from video at specified timestamp
   * @param {string} videoPath - Path to video file
   * @param {string|number} timestamp - Timestamp (HH:MM:SS or seconds)
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Screenshot metadata and path
   */
  async captureFrame(videoPath, timestamp, options = {}) {
    try {
      if (!fs.existsSync(videoPath)) {
        throw new Error(`Video file not found: ${videoPath}`);
      }

      console.log(`[ScreenshotTool] Raw timestamp input: ${timestamp} (type: ${typeof timestamp})`);
      const normalizedTimestamp = this.normalizeTimestamp(timestamp);
      console.log(`[ScreenshotTool] Normalized timestamp: ${normalizedTimestamp}`);
      const outputPath = this.generateOutputPath(videoPath, normalizedTimestamp);

      console.log(`[ScreenshotTool] Capturing frame at ${normalizedTimestamp} from ${path.basename(videoPath)}`);

      await this.extractFrame(videoPath, normalizedTimestamp, outputPath, options);
      
      const metadata = await this.analyzeScreenshot(outputPath);
      
      return {
        path: outputPath,
        timestamp: normalizedTimestamp,
        videoPath: videoPath,
        metadata: metadata,
        size: this.getFileSize(outputPath)
      };
      
    } catch (error) {
      console.error('[ScreenshotTool] Frame capture failed:', error.message);
      throw error;
    }
  }

  /**
   * Capture multiple frames around a timestamp for context
   * @param {string} videoPath - Path to video file
   * @param {string|number} centerTimestamp - Center timestamp
   * @param {Object} options - Options including frame count and interval
   * @returns {Promise<Array>} - Array of screenshot objects
   */
  async captureFrameSequence(videoPath, centerTimestamp, options = {}) {
    const frameCount = options.frameCount || 3;
    const interval = options.interval || 2; // seconds between frames
    const centerTime = this.timestampToSeconds(centerTimestamp);
    
    const screenshots = [];
    const startTime = centerTime - Math.floor(frameCount / 2) * interval;
    
    for (let i = 0; i < frameCount; i++) {
      const timestamp = startTime + (i * interval);
      if (timestamp >= 0) {
        try {
          const screenshot = await this.captureFrame(videoPath, timestamp, options);
          screenshots.push(screenshot);
        } catch (error) {
          console.warn(`[ScreenshotTool] Failed to capture frame at ${timestamp}s: ${error.message}`);
        }
      }
    }
    
    return screenshots;
  }

  /**
   * Extract frame using FFmpeg
   * @param {string} videoPath - Input video path
   * @param {string} timestamp - Normalized timestamp
   * @param {string} outputPath - Output image path
   * @param {Object} options - Additional FFmpeg options
   * @returns {Promise<void>}
   */
  async extractFrame(videoPath, timestamp, outputPath, options = {}) {
    // Normalize paths for Windows
    const normalizedVideoPath = path.resolve(videoPath);
    const normalizedOutputPath = path.resolve(outputPath);

    // Ensure output directory exists
    const outputDir = path.dirname(normalizedOutputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Get video duration first to validate timestamp
    const videoDuration = await this.getVideoDuration(normalizedVideoPath);
    const timestampSeconds = this.timestampToSeconds(timestamp);

    // If timestamp is beyond video duration, use a safer timestamp
    let safeTimestamp = timestamp;
    if (videoDuration && timestampSeconds > videoDuration) {
      safeTimestamp = this.secondsToTimestamp(Math.max(0, videoDuration - 10)); // 10 seconds before end
      console.log(`[ScreenshotTool] Timestamp ${timestamp} beyond video duration, using ${safeTimestamp}`);
    }

    // Use input seeking for better performance and accuracy
    const ffmpegArgs = [
      '-ss', safeTimestamp,
      '-i', `"${normalizedVideoPath}"`,
      '-vframes', '1',
      '-q:v', this.quality.toString(),
      '-vf', `scale=${this.maxWidth}:-1`,
      '-avoid_negative_ts', 'make_zero',
      '-y',
      `"${normalizedOutputPath}"`
    ];

    // Add any custom filters
    if (options.filters) {
      const filterIndex = ffmpegArgs.indexOf('-vf');
      ffmpegArgs[filterIndex + 1] += `,${options.filters}`;
    }

    const command = `ffmpeg ${ffmpegArgs.join(' ')}`;

    console.log(`[ScreenshotTool] Executing: ${command}`);

    try {
      const { stdout, stderr } = await execAsync(command, {
        timeout: 30000,
        windowsHide: true
      });

      // Log FFmpeg output for debugging
      if (stderr && stderr.includes('error')) {
        console.log(`[ScreenshotTool] FFmpeg stderr: ${stderr}`);
      }

      if (!fs.existsSync(normalizedOutputPath)) {
        // Try alternative approach with output seeking
        console.log(`[ScreenshotTool] First attempt failed, trying alternative approach...`);
        await this.extractFrameAlternative(normalizedVideoPath, safeTimestamp, normalizedOutputPath);
      }

      if (fs.existsSync(normalizedOutputPath)) {
        console.log(`[ScreenshotTool] Frame extracted successfully: ${path.basename(normalizedOutputPath)}`);
      } else {
        throw new Error(`Screenshot file was not created at: ${normalizedOutputPath}`);
      }

    } catch (error) {
      console.error(`[ScreenshotTool] FFmpeg command failed: ${command}`);
      console.error(`[ScreenshotTool] Video path exists: ${fs.existsSync(normalizedVideoPath)}`);
      console.error(`[ScreenshotTool] Output directory exists: ${fs.existsSync(outputDir)}`);
      throw new Error(`FFmpeg extraction failed: ${error.message}`);
    }
  }

  /**
   * Analyze screenshot for visual elements
   * @param {string} imagePath - Path to screenshot
   * @returns {Promise<Object>} - Analysis results
   */
  async analyzeScreenshot(imagePath) {
    try {
      // Basic image analysis using FFprobe
      const probeCommand = `ffprobe -v quiet -print_format json -show_streams "${imagePath}"`;
      const { stdout } = await execAsync(probeCommand);
      const probeData = JSON.parse(stdout);
      
      const videoStream = probeData.streams.find(s => s.codec_type === 'video');
      
      const metadata = {
        width: videoStream?.width || 0,
        height: videoStream?.height || 0,
        format: videoStream?.codec_name || 'unknown',
        aspectRatio: videoStream ? (videoStream.width / videoStream.height).toFixed(2) : '0',
        colorSpace: videoStream?.color_space || 'unknown',
        hasSubtitles: false, // MKV files don't have baked-in subtitles
        brightness: await this.analyzeBrightness(imagePath),
        timestamp: this.extractTimestampFromPath(imagePath)
      };
      
      return metadata;
      
    } catch (error) {
      console.warn('[ScreenshotTool] Screenshot analysis failed:', error.message);
      return {
        width: 0,
        height: 0,
        format: 'unknown',
        aspectRatio: '0',
        hasSubtitles: false,
        brightness: 'unknown'
      };
    }
  }



  /**
   * Get video duration in seconds
   * @param {string} videoPath - Path to video file
   * @returns {Promise<number|null>} - Duration in seconds or null if failed
   */
  async getVideoDuration(videoPath) {
    try {
      const command = `ffprobe -v quiet -print_format json -show_entries format=duration "${videoPath}"`;
      const { stdout } = await execAsync(command, { timeout: 10000 });
      const data = JSON.parse(stdout);
      return parseFloat(data.format?.duration) || null;
    } catch (error) {
      console.warn(`[ScreenshotTool] Failed to get video duration: ${error.message}`);
      return null;
    }
  }

  /**
   * Alternative frame extraction method using output seeking
   * @param {string} videoPath - Input video path
   * @param {string} timestamp - Timestamp
   * @param {string} outputPath - Output path
   * @returns {Promise<void>}
   */
  async extractFrameAlternative(videoPath, timestamp, outputPath) {
    const ffmpegArgs = [
      '-i', `"${videoPath}"`,
      '-ss', timestamp,
      '-vframes', '1',
      '-q:v', this.quality.toString(),
      '-vf', `scale=${this.maxWidth}:-1`,
      '-avoid_negative_ts', 'make_zero',
      '-y',
      `"${outputPath}"`
    ];

    const command = `ffmpeg ${ffmpegArgs.join(' ')}`;
    console.log(`[ScreenshotTool] Alternative extraction: ${command}`);

    try {
      await execAsync(command, { timeout: 30000, windowsHide: true });
    } catch (error) {
      console.warn(`[ScreenshotTool] Alternative extraction also failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Analyze image brightness
   * @param {string} imagePath - Path to image
   * @returns {Promise<string>} - Brightness description
   */
  async analyzeBrightness(imagePath) {
    try {
      // Use FFmpeg to analyze average brightness
      const command = `ffprobe -f lavfi -i "movie=${imagePath},signalstats" -show_entries frame_tags=lavfi.signalstats.YAVG -v quiet -of csv=p=0`;
      const { stdout } = await execAsync(command, { timeout: 10000 });

      const brightness = parseFloat(stdout.trim());
      if (brightness < 50) return 'dark';
      if (brightness > 200) return 'bright';
      return 'normal';

    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Normalize timestamp to FFmpeg format
   * @param {string|number} timestamp - Input timestamp
   * @returns {string} - Normalized timestamp (HH:MM:SS)
   */
  normalizeTimestamp(timestamp) {
    console.log(`[ScreenshotTool] normalizeTimestamp input: ${timestamp} (type: ${typeof timestamp})`);

    if (typeof timestamp === 'number') {
      console.log(`[ScreenshotTool] Converting number ${timestamp} to timestamp`);
      // Scene detector returns milliseconds, convert to seconds
      const seconds = timestamp / 1000;
      console.log(`[ScreenshotTool] Converted ${timestamp}ms to ${seconds}s`);
      const result = this.secondsToTimestamp(seconds);
      console.log(`[ScreenshotTool] Number conversion result: ${result}`);
      return result;
    }

    if (typeof timestamp === 'string') {
      // If already in HH:MM:SS format, return as-is
      if (/^\d{1,2}:\d{2}:\d{2}(\.\d+)?$/.test(timestamp)) {
        console.log(`[ScreenshotTool] String already in HH:MM:SS format: ${timestamp}`);
        return timestamp;
      }

      // If in seconds, convert
      const seconds = parseFloat(timestamp);
      if (!isNaN(seconds)) {
        console.log(`[ScreenshotTool] Converting string seconds ${seconds} to timestamp`);
        const result = this.secondsToTimestamp(seconds);
        console.log(`[ScreenshotTool] String seconds conversion result: ${result}`);
        return result;
      }
    }

    throw new Error(`Invalid timestamp format: ${timestamp}`);
  }

  /**
   * Convert seconds to HH:MM:SS format
   * @param {number} seconds - Seconds
   * @returns {string} - Formatted timestamp
   */
  secondsToTimestamp(seconds) {
    console.log(`[ScreenshotTool] secondsToTimestamp input: ${seconds}`);
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 100);

    console.log(`[ScreenshotTool] Breakdown: ${seconds}s -> ${hours}h ${minutes}m ${secs}s ${ms}ms`);
    const result = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
    console.log(`[ScreenshotTool] Final timestamp: ${result}`);
    return result;
  }

  /**
   * Convert timestamp to seconds
   * @param {string} timestamp - HH:MM:SS timestamp
   * @returns {number} - Seconds
   */
  timestampToSeconds(timestamp) {
    if (typeof timestamp === 'number') return timestamp;
    
    const parts = timestamp.split(':');
    if (parts.length === 3) {
      const [hours, minutes, seconds] = parts;
      return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds);
    }
    
    return parseFloat(timestamp) || 0;
  }

  /**
   * Generate output path for screenshot
   * @param {string} videoPath - Video file path
   * @param {string} timestamp - Timestamp
   * @returns {string} - Output path
   */
  generateOutputPath(videoPath, timestamp) {
    const videoName = path.basename(videoPath, path.extname(videoPath));
    const safeTimestamp = timestamp.replace(/[:.]/g, '-');
    const filename = `${videoName}_${safeTimestamp}.${this.format}`;
    return path.join(this.outputDir, filename);
  }

  /**
   * Extract timestamp from screenshot filename
   * @param {string} imagePath - Image path
   * @returns {string|null} - Extracted timestamp
   */
  extractTimestampFromPath(imagePath) {
    const filename = path.basename(imagePath, path.extname(imagePath));
    const match = filename.match(/_(\d{2}-\d{2}-\d{2}-\d{2})$/);
    return match ? match[1].replace(/-/g, ':') : null;
  }

  /**
   * Get file size in bytes
   * @param {string} filePath - File path
   * @returns {number} - File size
   */
  getFileSize(filePath) {
    try {
      return fs.statSync(filePath).size;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Ensure required directories exist
   * @returns {void}
   */
  ensureDirectories() {
    [this.outputDir, this.tempDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`[ScreenshotTool] Created directory: ${dir}`);
      }
    });
  }

  /**
   * Clean up old screenshots
   * @param {number} maxAge - Maximum age in milliseconds
   * @returns {Promise<void>}
   */
  async cleanup(maxAge = 24 * 60 * 60 * 1000) { // 24 hours default
    try {
      const files = fs.readdirSync(this.outputDir);
      const now = Date.now();
      
      for (const file of files) {
        const filePath = path.join(this.outputDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          console.log(`[ScreenshotTool] Cleaned up old screenshot: ${file}`);
        }
      }
    } catch (error) {
      console.warn('[ScreenshotTool] Cleanup failed:', error.message);
    }
  }
}
